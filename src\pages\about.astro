---
import Layout from '../layouts/Layout.astro';
import AboutTimeline from '../components/AboutTimeline.astro';
import StoryCard from '../components/StoryCard.astro';
import { getEntry } from 'astro:content';
import { siteConfig } from '../config/site';

const aboutData = await getEntry('about', 'profile');
const { title, timeline, stories } = aboutData.data;
---

<Layout title={`About | ${siteConfig.title}`}>
  <section class="about hero-refined bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40 relative overflow-hidden">
    <!-- Sophisticated background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-primary-300/6 to-accent-300/4 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-accent-300/4 to-primary-300/6 rounded-full blur-3xl animate-float" style="animation-delay: 2s; animation-duration: 5s;"></div>
      
      <!-- Refined texture overlay -->
      <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;80&quot; height=&quot;80&quot; viewBox=&quot;0 0 80 80&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23a19e92&quot; fill-opacity=&quot;0.03&quot;%3E%3Ccircle cx=&quot;40&quot; cy=&quot;40&quot; r=&quot;0.8&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="hero-title-refined animate-fade-in">
        <h1 class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          {title}
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded"></span>
        </h1>
      </div>
      
      <!-- Sophisticated content cards with glassmorphism -->
      <div class="space-y-12">
        {sections.map((section, sectionIndex) => (
          <div class="glass-card p-8 lg:p-10">
            <h2 class="heading-md text-text-light dark:text-text-dark mb-8 relative">
              {section.heading}
              <span class="absolute bottom-0 left-0 w-12 h-0.5 bg-gradient-to-r from-primary-500 to-accent-600 rounded mt-2"></span>
            </h2>
            
            {section.content && (
              <div class="mb-8">
                {section.content.split('\n\n').map((paragraph) => (
                  <p class="text-text-light-muted dark:text-text-dark-muted mb-6 text-lg leading-relaxed">{paragraph}</p>
                ))}
              </div>
            )}
            
            {section.subsections && section.subsections.map((subsection, subIndex) => (
              <div class="mt-8 bg-white/60 dark:bg-secondary-800/40 backdrop-blur-sm border border-secondary-200/30 dark:border-secondary-700/30 rounded-2xl p-6 hover:bg-white/80 dark:hover:bg-secondary-800/60 transition-all duration-300">
                {subsection.subheading && (
                  <h3 class="text-xl font-semibold text-primary-600 dark:text-primary-400 mb-6 font-heading flex items-center gap-3">
                    <span class="w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white text-xs">
                      {subIndex + 1}
                    </span>
                    {subsection.subheading}
                  </h3>
                )}
                
                {subsection.items && (
                  <ul class="space-y-3">
                    {subsection.items.map((item) => (
                      <li class="flex items-start gap-3">
                        <span class="text-primary-600 dark:text-primary-400 mt-1 font-bold text-lg flex-shrink-0">✓</span>
                        <span class="text-text-light-muted dark:text-text-dark-muted text-base leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                )}
                
                {subsection.content && (
                  <p class="text-text-light-muted dark:text-text-dark-muted text-base leading-relaxed">{subsection.content}</p>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>

      <!-- Enhanced CTA section -->
      <div class="text-center mt-16">
        <div class="glass-card p-8 text-center">
          <h3 class="heading-md text-text-light dark:text-text-dark mb-6">
            Let's Work Together
          </h3>
          <p class="text-lg text-text-light-muted dark:text-text-dark-muted mb-8 max-w-2xl mx-auto">
            Ready to build something amazing? I'm always excited to discuss new projects and opportunities.
          </p>
          <div class="flex justify-center gap-4 flex-col sm:flex-row">
            <a href="/contact" class="btn-primary group text-lg px-8 py-4">
              Get In Touch
              <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a href="/portfolio" class="btn-secondary group text-lg px-8 py-4">
              View My Work
              <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout> 
