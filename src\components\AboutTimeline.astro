---
interface TimelineItem {
  year: string;
  title: string;
  description: string;
  highlights?: string[];
  icon?: string;
}

interface Props {
  timeline: TimelineItem[];
}

const { timeline } = Astro.props;
---

<div class="timeline-container relative">
  <!-- Timeline line -->
  <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500/20 via-primary-500/40 to-primary-500/20 dark:from-primary-400/30 dark:via-primary-400/50 dark:to-primary-400/30"></div>
  
  <div class="space-y-8">
    {timeline.map((item, index) => (
      <div class="timeline-item relative flex items-start gap-6 group">
        <!-- Timeline marker -->
        <div class="relative z-10 flex-shrink-0">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 dark:from-primary-400 dark:to-primary-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border-4 border-white dark:border-secondary-800">
            <span class="text-white text-xl font-bold">
              {item.icon || (index + 1)}
            </span>
          </div>
          <!-- Connecting line to next item -->
          {index < timeline.length - 1 && (
            <div class="absolute top-16 left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-gradient-to-b from-primary-500/40 to-transparent dark:from-primary-400/50"></div>
          )}
        </div>

        <!-- Timeline content -->
        <div class="flex-1 min-w-0">
          <div class="bg-white/90 dark:bg-secondary-800/90 backdrop-blur-sm rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1 border border-secondary-200/50 dark:border-secondary-700/50">
            <!-- Year badge -->
            <div class="inline-flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-primary-500/10 to-accent-500/10 dark:from-primary-400/20 dark:to-accent-400/20 text-primary-700 dark:text-primary-300 text-sm font-semibold rounded-full border border-primary-500/20 dark:border-primary-400/30 mb-4">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              {item.year}
            </div>

            <!-- Title -->
            <h3 class="text-xl lg:text-2xl font-bold text-text-light dark:text-text-dark mb-3 font-heading">
              {item.title}
            </h3>

            <!-- Description -->
            <p class="text-text-light-muted dark:text-text-dark-muted text-base leading-relaxed mb-4">
              {item.description}
            </p>

            <!-- Highlights -->
            {item.highlights && item.highlights.length > 0 && (
              <div class="mt-4">
                <h4 class="text-sm font-semibold text-text-light-secondary dark:text-text-dark-secondary uppercase tracking-wider mb-3">
                  Key Achievements
                </h4>
                <ul class="space-y-2">
                  {item.highlights.map((highlight) => (
                    <li class="flex items-start gap-3">
                      <span class="text-primary-600 dark:text-primary-400 mt-1 font-bold text-sm flex-shrink-0">✓</span>
                      <span class="text-text-light-muted dark:text-text-dark-muted text-sm leading-relaxed">{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    ))}
  </div>
</div>

<style>
.timeline-item:nth-child(even) .timeline-content {
  animation-delay: 0.1s;
}

.timeline-item:nth-child(odd) .timeline-content {
  animation-delay: 0.2s;
}

@media (max-width: 768px) {
  .timeline-container {
    padding-left: 0;
  }
  
  .timeline-item {
    flex-direction: column;
    text-align: center;
  }
  
  .absolute.left-8 {
    display: none;
  }
}
</style>