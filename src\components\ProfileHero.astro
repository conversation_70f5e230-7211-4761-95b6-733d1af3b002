---
interface Props {
  name: string;
  introduction: string;
  valueProp: string;
  expertise: string[];
  profileImage?: string;
  ctaText?: string;
  ctaUrl?: string;
}

const { 
  name, 
  introduction, 
  valueProp, 
  expertise, 
  profileImage = "/profile-placeholder.jpg",
  ctaText = "Learn more about my journey",
  ctaUrl = "/about"
} = Astro.props;
---

<section class="profile-hero py-16 bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/20 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/20 relative overflow-hidden">
  <!-- Subtle background elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-primary-300/6 to-accent-300/4 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/3 left-1/4 w-64 h-64 bg-gradient-to-br from-accent-300/4 to-primary-300/6 rounded-full blur-3xl"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="max-w-4xl mx-auto">
      <!-- Profile Card -->
      <div class="glass-card p-8 lg:p-12 text-center lg:text-left">
        <div class="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          
          <!-- Profile Image -->
          <div class="flex-shrink-0">
            <div class="relative group">
              <div class="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-accent-600/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500"></div>
              <div class="relative w-32 h-32 lg:w-40 lg:h-40 rounded-full overflow-hidden border-4 border-white/50 dark:border-secondary-700/50 shadow-2xl group-hover:scale-105 transition-transform duration-500">
                <img 
                  src={profileImage} 
                  alt={`${name} - Professional headshot`}
                  class="w-full h-full object-cover"
                  loading="eager"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-primary-900/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>

          <!-- Profile Content -->
          <div class="flex-1 min-w-0">
            <!-- Introduction -->
            <div class="mb-6">
              <h2 class="text-2xl lg:text-3xl font-bold text-text-light dark:text-text-dark mb-3 font-heading">
                Hi, I'm <span class="text-primary-600 dark:text-primary-400">{name}</span>
              </h2>
              <p class="text-lg lg:text-xl text-text-light-secondary dark:text-text-dark-secondary font-medium leading-relaxed mb-4">
                {introduction}
              </p>
              <p class="text-base lg:text-lg text-text-light-muted dark:text-text-dark-muted leading-relaxed">
                {valueProp}
              </p>
            </div>

            <!-- Expertise Tags -->
            <div class="mb-8">
              <h3 class="text-sm font-semibold text-text-light-muted dark:text-text-dark-muted uppercase tracking-wider mb-3 text-center lg:text-left">
                Core Expertise
              </h3>
              <div class="flex flex-wrap gap-2 justify-center lg:justify-start">
                {expertise.map((skill) => (
                  <span class="px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-primary-500/10 to-accent-500/10 dark:from-primary-400/20 dark:to-accent-400/20 text-primary-700 dark:text-primary-300 text-xs sm:text-sm font-medium rounded-full border border-primary-500/20 dark:border-primary-400/30 hover:bg-gradient-to-r hover:from-primary-500/20 hover:to-accent-500/20 dark:hover:from-primary-400/30 dark:hover:to-accent-400/30 transition-all duration-300 hover:scale-105">
                    {skill}
                  </span>
                ))}
              </div>
            </div>

            <!-- CTA Button -->
            <div class="flex justify-center lg:justify-start">
              <a 
                href={ctaUrl} 
                class="group inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:scale-105"
                aria-label="Learn more about my background and experience"
              >
                {ctaText}
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>